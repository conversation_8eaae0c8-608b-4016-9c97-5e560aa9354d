package org.easitline.common.db.impl;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.easitline.common.core.context.ServerContext;
import org.easitline.common.db.log.JDBCLogger;
import org.easitline.common.db.log.JDBCWarnLogger;

public class QueryStrategy {

	private static int thisIndex = -1;
	
	private static Map<String,Integer>[] caches = null;
	
	private static long queryThreadCount = 0;
	
	private static long logtimer = System.currentTimeMillis();
	
	/**
	 * 当前查询加1
	 * @return
	 */
	public static  synchronized long addQueryCount(){
		queryThreadCount++;
		return queryThreadCount;
	}
	
	/**
	 * 当前执行查询减1
	 * @return
	 */
	public static synchronized void subQueryCount(){
		queryThreadCount--;
		if(queryThreadCount<=0)  queryThreadCount = 0;
		//每五分钟打印一次
		if(System.currentTimeMillis() - logtimer > 300*1000){
			logtimer = System.currentTimeMillis();
			JDBCLogger.getLogger().info("当前执行的查询线程数为："+queryThreadCount);
			if(queryThreadCount>100){
				JDBCWarnLogger.getDangerLogger().warn("当前执行的查询线程数为："+queryThreadCount);
			}
		}
	}
	
	public static synchronized void addQueryTimeout(String sql){
		if(thisIndex == -1){
			caches = new ConcurrentHashMap[10];
			for(int i = 0 ;i <= 9 ;i++){
				caches[i] = new  ConcurrentHashMap<String,Integer>();
			}
		}
		int _index  = (int)(System.currentTimeMillis()/1000%1000/100);
		//System.out.println("_index->"+_index);
		if(thisIndex!=_index){
			for(int i = 0 ;i <= 9 ;i++){
				caches[i].clear();
			}
			thisIndex = _index;
		}
		int executeCount = 1 ;
		if(caches[thisIndex].containsKey(sql)){
			executeCount = caches[thisIndex].get(sql);
			executeCount++;
			caches[thisIndex].put(sql, executeCount);
		}else{
			//防止内存溢出,最大缓存超时SQL不超过1000;
			if(caches[thisIndex].size()>1000){
				JDBCLogger.getLogger().info("caches["+thisIndex+"].size() 超过1000条SQL语句执行超时5S，不再加入控制策略！");
				JDBCWarnLogger.getDangerLogger().warn("caches["+thisIndex+"].size() 超过1000条SQL语句执行超过5S，不再加入控制策略！");
				caches[thisIndex].put(sql, executeCount);
			}
		}
		
		JDBCLogger.getLogger().info("最近100秒，查询执行时间超过5S的次数："+caches[thisIndex].size()+",sql->"+sql);
	}
	
	/**
	 * 保存每100秒执行超时的sql执行情况。
	 * @param sql
	 * @param addFlag
	 * @return
	 */
	public synchronized static int getQueryMaxTime(String sql,int defaultMaxTime){
		
		if(thisIndex == -1){
			caches = new ConcurrentHashMap[10];
			for(int i = 0 ;i <= 9 ;i++){
				caches[i] = new  ConcurrentHashMap<String,Integer>();
			}
		}
		
		int _index  = (int)(System.currentTimeMillis()/1000%1000/100);
		//System.out.println("_index->"+_index);
		if(thisIndex!=_index){
			for(int i = 0 ;i <= 9 ;i++){
				caches[i].clear();
			}
			thisIndex = _index;
			JDBCLogger.getLogger().info("EasyQuery执行快照["+thisIndex+"]：当前执行的查询线程数->"+queryThreadCount+",最近100S查询执行时间超过5S的SQL个数："+caches[thisIndex].size());
		}
		
		Map<String,Integer> map = caches[thisIndex];
		
		if(map == null) {
			if(ServerContext.isDebug())  JDBCLogger.getLogger().debug("QueryStrategy.getQueryMaxTime() fail,caches["+thisIndex+"] is null!");
			return defaultMaxTime;
		}
		
		Integer executeCount = map.get(sql);
		if(executeCount == null) return defaultMaxTime;
		
		if(executeCount>50) {
			JDBCLogger.getLogger().warn("最近100秒内SQL执行超时次数为："+executeCount+",系统主动降低SQL的执行时间为："+executeCount+"s，sql->"+sql);
			JDBCWarnLogger.getDangerLogger().warn("最近100秒内SQL执行超时次数为："+executeCount+",系统主动降低SQL的执行时间为："+executeCount+"s，sql->"+sql);
			return 5;
		}
		if(executeCount>30){
			JDBCLogger.getLogger().warn("最近100秒内SQL执行超时次数为："+executeCount+",系统主动降低SQL的执行时间为："+executeCount+"s，sql->"+sql);
			JDBCWarnLogger.getDangerLogger().warn("最近100秒内SQL执行超时次数为："+executeCount+",系统主动降低SQL的执行时间为："+executeCount+"s，sql->"+sql);
			return 10;
		}
		if(executeCount>10){
			JDBCLogger.getLogger().warn("最近100秒内SQL执行超时次数为："+executeCount+",系统主动降低SQL的执行时间为："+executeCount+"s，sql->"+sql);
			JDBCWarnLogger.getDangerLogger().warn("最近100秒内SQL执行超时次数为："+executeCount+",系统主动降低SQL的执行时间为："+executeCount+"s，sql->"+sql);
			return 20;
		}
		return defaultMaxTime;
	}
	
}
