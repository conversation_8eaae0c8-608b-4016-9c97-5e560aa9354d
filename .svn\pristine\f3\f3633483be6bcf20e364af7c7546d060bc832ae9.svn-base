package org.easitline.common.db.impl;

import java.sql.ResultSet;

import org.easitline.common.db.EasyRowMapper;
import org.easitline.common.db.log.JDBCLogger;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;

public class JSONMapperImpl implements EasyRowMapper<JSONObject> {

	@SuppressWarnings("unchecked")
	@Override
	public JSONObject mapRow(ResultSet rs,int convertField) {
		try {
			java.sql.ResultSetMetaData meta = rs.getMetaData();
			// 取得列数
			int columnCount = meta.getColumnCount();
			// 保存列相关的信息
			String[] column = new String[columnCount];

			for (int i = 0; i < columnCount; i++) {
				if(convertField==2){
					column[i] = meta.getColumnLabel(i + 1).toUpperCase(); // 取得每一列的列名
				}else if(convertField==1){
					column[i] = meta.getColumnLabel(i + 1).toLowerCase();
				}else if(convertField==3) {
					column[i] = StringUtils.toCamelCase(meta.getColumnLabel(i + 1));
				}else{
					column[i] = meta.getColumnLabel(i + 1);
				}
			}
			JSONObject row = new JSONObject(true);
			for (int i = 0; i < columnCount; i++) {
				String value=rs.getString(i + 1);
				if(value==null||"".equals("null")){
					value="";
				}
				row.put(column[i], value);
			}
			return row;
		} catch (Exception ex) {
			JDBCLogger.getLogger().error("EasyMapMapperImpl.mapRow() exception , cause:"+ex.getMessage());
		}
		return null;
	}

}
